// Affiche les 5 dernières notifications non archivées sur la page de dashboard

import React, { useEffect, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNotifications } from '../../../hooks/useNotifications';
import { useCreateNotification } from '../../../hooks/useCreateNotification';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import logger from '../../../utils/logger';
import { CheckSquare, ArrowRight, HelpCircle } from 'lucide-react';
import DOMPurify from 'dompurify';
import { Tooltip } from '@mui/material';

const getNotificationStyle = (type: string) => {
  switch (type) {
    case 'mission':
      return 'bg-red-50 text-red-700';
    case 'jobi':
      return 'bg-yellow-50 text-yellow-700';
    case 'message':
      return 'bg-blue-50 text-blue-700';
    case 'bug_report':
      return 'bg-green-50 text-green-700';
    case 'invoice':
      return 'bg-purple-50 text-purple-700';
    default:
      return 'bg-gray-50 text-gray-700';
  }
};

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'jobi':
      return '💰';
    case 'message':
      return '✉️';
    case 'mission':
      return '🎯';
    case 'mission_comment':
      return '💬';
    case 'bug_report':
      return '🛠️';
    case 'invoice':
      return '📝';
    case 'subscription':
      return '🔄';
    default:
      return 'ℹ️';
  }
};

const RecentNotifications: React.FC = () => {
  const { notifications, loading, fetchNotifications, toggleReadStatus } = useNotifications();
  const { markAsRead } = useCreateNotification();
  const navigate = useNavigate();
  const isFirstRender = useRef(true);
  const prevNotifications = useRef<typeof notifications>();

  // Charger les notifications au montage du composant
  useEffect(() => {
    fetchNotifications(false, { page: 1, unread: true });
  }, [fetchNotifications]);

  // Mémoiser les notifications récentes non lues pour éviter les recalculs inutiles
  const recentNotifications = useMemo(() => {
    const unreadNotifications = notifications?.filter(notif => !notif.is_read) || [];
    const slicedNotifications = unreadNotifications.slice(0, 4);
    
    // Ne logger que si c'est le premier rendu ou si les notifications ont changé
    if (isFirstRender.current || JSON.stringify(prevNotifications.current) !== JSON.stringify(notifications)) {
      logger.info('Notifications récentes non lues:', slicedNotifications);
      prevNotifications.current = notifications;
      isFirstRender.current = false;
    }
    
    return slicedNotifications;
  }, [notifications]);

  // Gérer le clic sur une notification
  const handleNotificationClick = async (notificationId: string, link?: string) => {
    try {
      // Marquer la notification comme lue
      await markAsRead(notificationId);
      // Rafraîchir les notifications
      await fetchNotifications(false, { page: 1, unread: true });
      // Rediriger vers le lien si présent
      if (link) {
        navigate(link);
      }
    } catch (error) {
      logger.error('Erreur lors du marquage de la notification comme lue:', error);
    }
  };

  // Gérer le clic sur "Tout marquer comme lu"
  const handleMarkAllAsRead = async () => {
    try {
      // Marquer chaque notification affichée comme lue
      await Promise.all(
        recentNotifications.map(notification => 
          toggleReadStatus(notification.id, true)
        )
      );
      // Rafraîchir les notifications en forçant le rafraîchissement du cache
      await fetchNotifications(false, { page: 1, unread: true }, true);
    } catch (error) {
      logger.error('Erreur lors du marquage des notifications comme lues:', error);
    }
  };

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4 text-gray-800">Dernières notifications</h2>
        <div className="animate-pulse space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          Dernières notifications
          <Tooltip 
            title="Retrouvez ici vos notifications les plus récentes non lues. Vous pouvez les marquer comme lues ou accéder à toutes vos notifications." 
            placement="top"
            enterTouchDelay={0}
            leaveTouchDelay={5000}
          >
            <button 
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
              }}
              className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors"
              aria-label="Plus d'informations sur les Dernières notifications"
            >
              <HelpCircle className="h-4 w-4 text-gray-400" />
            </button>
          </Tooltip>
        </h2>
        <div className="flex items-center gap-2">
          {recentNotifications.length > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium bg-[#FFF8F3] text-[#FF6B2C] hover:bg-[#FFE4BA] rounded-lg transition-all duration-200 transform hover:scale-105"
              title="Marquer ces notifications comme lues"
            >
              <CheckSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Tout marquer lu</span>
            </button>
          )}
          <button
            onClick={() => navigate('/dashboard/notifications')}
            className="flex items-center gap-1 px-3 py-1.5 text-sm font-medium text-[#FF6B2C] hover:bg-[#FFF8F3] rounded-lg transition-all duration-200 transform hover:scale-105"
          >
            <span>En voir plus</span>
            <ArrowRight className="h-4 w-4" />
          </button>
        </div>
      </div>
      
      {recentNotifications.length === 0 ? (
        <div className="text-center py-4 text-gray-500">
          Aucune notification non lue
        </div>
      ) : (
        <div className="space-y-3">
          {recentNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`p-3 rounded-lg ${getNotificationStyle(notification.type)}`}
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <span className="text-2xl transform hover:scale-110 transition-transform duration-200" role="img" aria-label={notification.type}>
                    {getNotificationIcon(notification.type)}
                  </span>
                </div>
                <div className="flex-grow">
                  <h3 className="font-medium text-gray-900">{notification.title}</h3>
                  <p className="text-sm text-gray-600 mt-1" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(notification.content) }}></p>
                  {notification.link && (
                    <a
                      className="text-[#FF6B2C] hover:text-[#FF7A35] hover:underline text-sm mt-2 inline-block transition-colors duration-200"
                      href={notification.link}
                      onClick={(e) => {
                        e.preventDefault();
                        handleNotificationClick(notification.id, notification.link);
                      }}
                      data-discover="true"
                    >
                      Consulter le lien
                    </a>
                  )}
                  <p className="text-gray-400 text-sm mt-2">
                    {format(new Date(notification.created_at), 'PPP à HH:mm', { locale: fr })}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecentNotifications; 